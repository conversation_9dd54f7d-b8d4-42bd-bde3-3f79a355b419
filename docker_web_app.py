#!/usr/bin/env python3
"""
Docker环境优化的Python Web应用
解决Docker容器中Web应用访问问题
端口：1111
"""

from flask import Flask, render_template_string, request, jsonify
import os
import sys
import json
import datetime
import threading
import time
import socket

# 应用配置
app = Flask(__name__)
app.config['SECRET_KEY'] = 'docker-web-app-secret-key'

# 全局变量
app_stats = {
    'start_time': datetime.datetime.now(),
    'requests_count': 0,
    'last_request': None
}

def log_request():
    """记录请求"""
    app_stats['requests_count'] += 1
    app_stats['last_request'] = datetime.datetime.now()

@app.before_request
def before_request():
    """请求前处理"""
    log_request()

@app.route('/')
def index():
    """主页"""
    return render_template_string("""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Docker Python Web应用</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            min-height: 100vh;
        }
        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- 标题 -->
                <div class="text-center mb-5">
                    <h1 class="display-4 text-white mb-4">
                        <i class="fas fa-rocket"></i> Docker Python Web应用
                    </h1>
                    <p class="lead text-white">
                        运行在Docker容器中的Python Web应用 - 端口1111
                    </p>
                </div>

                <!-- 状态卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-server fa-2x text-primary mb-2"></i>
                                <h6>服务器状态</h6>
                                <span class="status-indicator status-online"></span>
                                <small>运行中</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-network-wired fa-2x text-success mb-2"></i>
                                <h6>网络连接</h6>
                                <span class="status-indicator status-online"></span>
                                <small>正常</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-docker fa-2x text-info mb-2"></i>
                                <h6>Docker环境</h6>
                                <span class="status-indicator status-online"></span>
                                <small>已检测</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h6>运行时间</h6>
                                <small id="uptime">计算中...</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主要信息 -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle"></i> 应用信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Python版本:</strong></td>
                                        <td>{{ python_version }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Flask版本:</strong></td>
                                        <td>{{ flask_version }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>启动时间:</strong></td>
                                        <td>{{ start_time }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>请求次数:</strong></td>
                                        <td id="requestCount">{{ request_count }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>当前时间:</strong></td>
                                        <td id="currentTime">{{ current_time }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-network-wired"></i> 网络信息
                                </h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>主机名:</strong></td>
                                        <td>{{ hostname }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>本地IP:</strong></td>
                                        <td>{{ local_ip }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>监听端口:</strong></td>
                                        <td>1111</td>
                                    </tr>
                                    <tr>
                                        <td><strong>绑定地址:</strong></td>
                                        <td>0.0.0.0 (所有接口)</td>
                                    </tr>
                                    <tr>
                                        <td><strong>访问地址:</strong></td>
                                        <td>
                                            <a href="http://localhost:1111" target="_blank">localhost:1111</a>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能测试 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-vial"></i> 功能测试
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-primary w-100" onclick="testAPI()">
                                    <i class="fas fa-plug"></i> 测试API
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-success w-100" onclick="testHealth()">
                                    <i class="fas fa-heartbeat"></i> 健康检查
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-info w-100" onclick="refreshStats()">
                                    <i class="fas fa-sync"></i> 刷新统计
                                </button>
                            </div>
                        </div>
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>

                <!-- Docker信息 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fab fa-docker"></i> Docker环境信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>环境检测</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Docker容器环境</li>
                                    <li><i class="fas fa-check text-success"></i> 网络端口映射</li>
                                    <li><i class="fas fa-check text-success"></i> Python运行环境</li>
                                    <li><i class="fas fa-check text-success"></i> Flask Web服务</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>访问说明</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-info-circle text-info"></i> 容器内访问: localhost:1111</li>
                                    <li><i class="fas fa-info-circle text-info"></i> 主机访问: 需要端口映射</li>
                                    <li><i class="fas fa-info-circle text-info"></i> 网络模式: bridge</li>
                                    <li><i class="fas fa-info-circle text-info"></i> 协议: HTTP</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新运行时间
        function updateUptime() {
            const startTime = new Date('{{ start_time }}');
            const now = new Date();
            const diff = now - startTime;
            
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            document.getElementById('uptime').textContent = 
                hours + '时' + minutes + '分' + seconds + '秒';
        }

        // 更新当前时间
        function updateCurrentTime() {
            document.getElementById('currentTime').textContent = 
                new Date().toLocaleString('zh-CN');
        }

        // 测试API
        function testAPI() {
            showTestResult('正在测试API...', 'info');
            fetch('/api/test')
                .then(response => response.json())
                .then(data => {
                    showTestResult('API测试成功: ' + data.message, 'success');
                })
                .catch(error => {
                    showTestResult('API测试失败: ' + error, 'danger');
                });
        }

        // 健康检查
        function testHealth() {
            showTestResult('正在进行健康检查...', 'info');
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    showTestResult('健康检查通过: ' + data.status, 'success');
                })
                .catch(error => {
                    showTestResult('健康检查失败: ' + error, 'danger');
                });
        }

        // 刷新统计
        function refreshStats() {
            showTestResult('正在刷新统计...', 'info');
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('requestCount').textContent = data.requests_count;
                    showTestResult('统计数据已刷新', 'success');
                })
                .catch(error => {
                    showTestResult('刷新失败: ' + error, 'danger');
                });
        }

        // 显示测试结果
        function showTestResult(message, type) {
            const alertClass = 'alert-' + type;
            document.getElementById('testResults').innerHTML = 
                '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                message +
                '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                '</div>';
        }

        // 定时更新
        setInterval(updateUptime, 1000);
        setInterval(updateCurrentTime, 1000);
        
        // 初始化
        updateUptime();
        updateCurrentTime();
    </script>
</body>
</html>
    """, 
    python_version=f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
    flask_version=getattr(__import__('flask'), '__version__', 'Unknown'),
    start_time=app_stats['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
    current_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    request_count=app_stats['requests_count'],
    hostname=socket.gethostname(),
    local_ip=get_local_ip()
    )

@app.route('/api/test')
def api_test():
    """API测试接口"""
    return jsonify({
        'status': 'success',
        'message': 'API正常工作',
        'timestamp': datetime.datetime.now().isoformat(),
        'server': 'Docker Python Web App'
    })

@app.route('/health')
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'uptime': str(datetime.datetime.now() - app_stats['start_time']),
        'requests_count': app_stats['requests_count'],
        'last_request': app_stats['last_request'].isoformat() if app_stats['last_request'] else None
    })

@app.route('/api/stats')
def api_stats():
    """统计信息接口"""
    return jsonify({
        'requests_count': app_stats['requests_count'],
        'start_time': app_stats['start_time'].isoformat(),
        'uptime_seconds': (datetime.datetime.now() - app_stats['start_time']).total_seconds(),
        'last_request': app_stats['last_request'].isoformat() if app_stats['last_request'] else None,
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'hostname': socket.gethostname(),
        'local_ip': get_local_ip()
    })

def get_local_ip():
    """获取本地IP地址"""
    try:
        # 连接到外部地址来获取本地IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "127.0.0.1"

def print_startup_info():
    """打印启动信息"""
    print("=" * 60)
    print("🐳 Docker Python Web应用启动")
    print("=" * 60)
    print(f"📅 启动时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"🌐 Flask版本: {getattr(__import__('flask'), '__version__', 'Unknown')}")
    print(f"🏠 主机名: {socket.gethostname()}")
    print(f"🔗 本地IP: {get_local_ip()}")
    print(f"📁 工作目录: {os.getcwd()}")
    print("=" * 60)
    print("🌐 访问地址:")
    print("  📍 容器内: http://localhost:1111")
    print("  📍 容器内: http://127.0.0.1:1111")
    print(f"  📍 局域网: http://{get_local_ip()}:1111")
    print("=" * 60)
    print("🔧 Docker访问说明:")
    print("  1. 确保Docker端口映射: -p 1111:1111")
    print("  2. 主机访问: http://localhost:1111")
    print("  3. 网络模式: bridge (默认)")
    print("=" * 60)

if __name__ == '__main__':
    # 打印启动信息
    print_startup_info()
    
    # 启动Flask应用
    # 关键：绑定到0.0.0.0而不是127.0.0.1，这样Docker容器外部才能访问
    app.run(
        host='0.0.0.0',  # 绑定所有网络接口
        port=1111,       # 端口1111
        debug=True,      # 开启调试模式
        threaded=True,   # 支持多线程
        use_reloader=False  # 在Docker中禁用重载器避免问题
    )
