#!/usr/bin/env python3
"""
Docker Web应用故障排除脚本
诊断和解决Docker中Web应用访问问题
"""

import subprocess
import sys
import os
import socket
import time
import json
from datetime import datetime

def run_command(command, timeout=10):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "命令超时"
    except Exception as e:
        return -1, "", str(e)

def check_docker_environment():
    """检查Docker环境"""
    print("=" * 60)
    print("🐳 Docker环境检查")
    print("=" * 60)
    
    # 检查是否在Docker容器中
    docker_indicators = [
        '/.dockerenv',
        '/proc/1/cgroup'
    ]
    
    in_docker = False
    for indicator in docker_indicators:
        if os.path.exists(indicator):
            print(f"✅ 检测到Docker环境: {indicator}")
            in_docker = True
            break
    
    if not in_docker:
        print("⚠️  未检测到Docker环境")
        return False
    
    # 检查容器信息
    if os.path.exists('/proc/1/cgroup'):
        try:
            with open('/proc/1/cgroup', 'r') as f:
                cgroup_info = f.read()
                if 'docker' in cgroup_info:
                    print("✅ 确认在Docker容器中")
                else:
                    print("⚠️  可能在其他容器环境中")
        except:
            pass
    
    return True

def check_network_configuration():
    """检查网络配置"""
    print("\n" + "=" * 60)
    print("🌐 网络配置检查")
    print("=" * 60)
    
    # 检查主机名
    hostname = socket.gethostname()
    print(f"主机名: {hostname}")
    
    # 检查网络接口
    try:
        # 获取本地IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            print(f"✅ 本地IP: {local_ip}")
    except Exception as e:
        print(f"❌ 获取本地IP失败: {e}")
        local_ip = "127.0.0.1"
    
    # 检查端口1111是否可用
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('0.0.0.0', 1111))
            print("✅ 端口1111可用")
            port_available = True
    except OSError as e:
        print(f"❌ 端口1111不可用: {e}")
        port_available = False
    
    return local_ip, port_available

def check_python_environment():
    """检查Python环境"""
    print("\n" + "=" * 60)
    print("🐍 Python环境检查")
    print("=" * 60)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查Flask
    try:
        import flask
        print(f"✅ Flask版本: {flask.__version__}")
        flask_available = True
    except ImportError:
        print("❌ Flask未安装")
        flask_available = False
    
    return flask_available

def test_web_server():
    """测试Web服务器"""
    print("\n" + "=" * 60)
    print("🌐 Web服务器测试")
    print("=" * 60)
    
    # 启动简单的测试服务器
    test_server_code = '''
import socket
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        response = """
        <html>
        <head><title>Docker测试服务器</title></head>
        <body>
            <h1>🐳 Docker Web测试成功!</h1>
            <p>如果您看到这个页面，说明Docker网络配置正常。</p>
            <p>时间: """ + str(time.time()) + """</p>
        </body>
        </html>
        """
        self.wfile.write(response.encode())
    
    def log_message(self, format, *args):
        pass  # 禁用日志

def start_test_server():
    try:
        server = HTTPServer(('0.0.0.0', 1111), TestHandler)
        print("测试服务器启动在端口1111...")
        server.timeout = 5
        server.handle_request()
        server.server_close()
        return True
    except Exception as e:
        print(f"测试服务器启动失败: {e}")
        return False

if __name__ == "__main__":
    start_test_server()
'''
    
    # 写入测试服务器代码
    with open('test_server.py', 'w') as f:
        f.write(test_server_code)
    
    print("启动测试服务器...")
    returncode, stdout, stderr = run_command(f"{sys.executable} test_server.py", timeout=10)
    
    if returncode == 0:
        print("✅ 测试服务器启动成功")
        return True
    else:
        print(f"❌ 测试服务器启动失败: {stderr}")
        return False

def generate_docker_commands():
    """生成Docker运行命令"""
    print("\n" + "=" * 60)
    print("🐳 Docker运行命令")
    print("=" * 60)
    
    commands = [
        "# 基本运行命令",
        "docker run -p 1111:1111 -v $(pwd):/app -w /app python:3.9 python docker_web_app.py",
        "",
        "# 后台运行",
        "docker run -d -p 1111:1111 -v $(pwd):/app -w /app --name python-web python:3.9 python docker_web_app.py",
        "",
        "# 交互式运行（调试用）",
        "docker run -it -p 1111:1111 -v $(pwd):/app -w /app python:3.9 bash",
        "",
        "# 使用docker-compose",
        "# 创建docker-compose.yml文件，然后运行:",
        "# docker-compose up",
    ]
    
    for cmd in commands:
        print(cmd)

def create_docker_compose():
    """创建docker-compose.yml文件"""
    compose_content = '''version: '3.8'

services:
  python-web:
    image: python:3.9
    ports:
      - "1111:1111"
    volumes:
      - .:/app
    working_dir: /app
    command: python docker_web_app.py
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
'''
    
    with open('docker-compose.yml', 'w') as f:
        f.write(compose_content)
    
    print("✅ 创建了docker-compose.yml文件")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("💡 解决方案")
    print("=" * 60)
    
    solutions = [
        "1. 确保Docker端口映射正确: -p 1111:1111",
        "2. 应用必须绑定到0.0.0.0而不是127.0.0.1",
        "3. 检查防火墙设置",
        "4. 确认Docker网络模式为bridge",
        "5. 使用docker logs查看容器日志",
        "6. 尝试在容器内部测试: curl http://localhost:1111"
    ]
    
    print("常见解决方案:")
    for solution in solutions:
        print(f"  {solution}")
    
    print(f"\n推荐的启动命令:")
    print(f"  python docker_web_app.py")
    print(f"\n或者使用docker-compose:")
    print(f"  docker-compose up")

def main():
    """主函数"""
    print("🐳 Docker Web应用故障排除")
    print(f"诊断时间: {datetime.now()}")
    print(f"Python版本: {sys.version_info}")
    
    # 执行检查
    docker_ok = check_docker_environment()
    local_ip, port_ok = check_network_configuration()
    python_ok = check_python_environment()
    
    # 生成解决方案
    generate_docker_commands()
    create_docker_compose()
    provide_solutions()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 诊断总结")
    print("=" * 60)
    
    print(f"Docker环境: {'✅ 正常' if docker_ok else '❌ 异常'}")
    print(f"端口1111: {'✅ 可用' if port_ok else '❌ 被占用'}")
    print(f"Python环境: {'✅ 正常' if python_ok else '❌ 异常'}")
    
    if docker_ok and port_ok and python_ok:
        print("\n🎉 环境检查通过！可以启动Web应用")
        print("运行命令: python docker_web_app.py")
    else:
        print("\n⚠️  发现问题，请根据上述解决方案进行修复")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n诊断被中断")
    except Exception as e:
        print(f"\n诊断发生错误: {e}")
